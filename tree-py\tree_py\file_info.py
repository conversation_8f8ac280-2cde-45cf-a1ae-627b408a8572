"""
File information gathering module using functional programming principles.

This module provides pure functions for extracting file system information
and creating immutable FileInfo records. All functions use the Result type
for error handling.
"""

import os
import stat
from pathlib import Path
from datetime import datetime
from typing import Generator

from expression import Result, Ok, Error, effect
from expression.collections import Block

from .types import FileInfo, TreeConfig


def safe_stat(path: Path) -> Result[os.stat_result, str]:
    """Safely get file statistics."""
    try:
        return Ok(path.stat())
    except (OSError, IOError) as e:
        return Error(f"Cannot stat {path}: {e}")


def safe_lstat(path: Path) -> Result[os.stat_result, str]:
    """Safely get link statistics (doesn't follow symlinks)."""
    try:
        return Ok(path.lstat())
    except (OSError, IOError) as e:
        return Error(f"Cannot lstat {path}: {e}")


def safe_readlink(path: Path) -> Result[str, str]:
    """Safely read symbolic link target."""
    try:
        return Ok(os.readlink(path))
    except (<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>rro<PERSON>) as e:
        return Error(f"Cannot read link {path}: {e}")


def create_file_info(path: Path, config: TreeConfig) -> Result[FileInfo, str]:
    """
    Create FileInfo from a path using Result type for error handling.

    This function uses the Result type to handle errors gracefully.
    If any step fails, the entire operation returns an Error.
    """
    # Get link stats first (doesn't follow symlinks)
    lstat_result = safe_lstat(path)

    if lstat_result.is_error():
        return lstat_result

    stat_info = lstat_result.ok

    # Initialize basic info
    name = path.name
    size = stat_info.st_size
    mode = stat_info.st_mode
    uid = stat_info.st_uid
    gid = stat_info.st_gid
    atime = datetime.fromtimestamp(stat_info.st_atime)
    mtime = datetime.fromtimestamp(stat_info.st_mtime)
    ctime = datetime.fromtimestamp(stat_info.st_ctime)

    # Determine file type
    is_link = stat.S_ISLNK(mode)
    is_dir = stat.S_ISDIR(mode)
    is_socket = stat.S_ISSOCK(mode)
    is_fifo = stat.S_ISFIFO(mode)
    is_executable = bool(mode & (stat.S_IXUSR | stat.S_IXGRP | stat.S_IXOTH))

    # Handle symbolic links
    link_target = None
    if is_link:
        link_result = safe_readlink(path)
        if link_result.is_ok():
            link_target = link_result.value
            # For directories, check if link target is a directory
            if config.follow_links:
                stat_result = safe_stat(path)
                if stat_result.is_ok():
                    is_dir = stat.S_ISDIR(stat_result.value.st_mode)
        else:
            link_target = "[Error reading link]"

    # Get device and inode if requested
    device = stat_info.st_dev if config.show_device else None
    inode = stat_info.st_ino if config.show_inode else None

    return Ok(FileInfo(
        name=name,
        path=path,
        size=size,
        mode=mode,
        uid=uid,
        gid=gid,
        atime=atime,
        mtime=mtime,
        ctime=ctime,
        is_dir=is_dir,
        is_link=is_link,
        is_executable=is_executable,
        is_socket=is_socket,
        is_fifo=is_fifo,
        link_target=link_target,
        device=device,
        inode=inode,
        error=None
    ))


def should_include_file(file_info: FileInfo, config: TreeConfig) -> bool:
    """
    Pure function to determine if a file should be included based on config.
    """
    # Check if hidden file
    if not config.show_all and file_info.name.startswith('.'):
        return False
    
    # Check if directories only
    if config.dirs_only and not file_info.is_dir:
        return False
    
    # Check include patterns
    if config.include_patterns:
        from .filters import matches_any_pattern
        if not matches_any_pattern(file_info.name, config.include_patterns, config.ignore_case):
            return False
    
    # Check exclude patterns
    if config.exclude_patterns:
        from .filters import matches_any_pattern
        if matches_any_pattern(file_info.name, config.exclude_patterns, config.ignore_case):
            return False
    
    return True


def list_directory_contents(directory: Path, config: TreeConfig) -> Result[Block[FileInfo], str]:
    """
    List directory contents and return FileInfo objects.
    
    Returns a Result containing either a Block of FileInfo objects or an error message.
    """
    try:
        if not directory.is_dir():
            return Error(f"{directory} is not a directory")
        
        entries = []
        for entry in directory.iterdir():
            file_info_result = create_file_info(entry, config)
            match file_info_result:
                case Ok(file_info):
                    if should_include_file(file_info, config):
                        entries.append(file_info)
                case Error(error_msg):
                    # Create error FileInfo
                    error_info = FileInfo(
                        name=entry.name,
                        path=entry,
                        size=0,
                        mode=0,
                        uid=0,
                        gid=0,
                        atime=datetime.now(),
                        mtime=datetime.now(),
                        ctime=datetime.now(),
                        is_dir=False,
                        is_link=False,
                        is_executable=False,
                        is_socket=False,
                        is_fifo=False,
                        error=error_msg
                    )
                    entries.append(error_info)
        
        return Ok(Block.of_seq(entries))
    
    except (OSError, IOError) as e:
        return Error(f"Cannot read directory {directory}: {e}")


def format_file_size(size: int, human_readable: bool = False, si_units: bool = False) -> str:
    """
    Format file size for display.
    
    Args:
        size: Size in bytes
        human_readable: Use human-readable format (K, M, G, etc.)
        si_units: Use SI units (powers of 1000) instead of binary (powers of 1024)
    """
    if not human_readable:
        return str(size)
    
    if size == 0:
        return "0"
    
    units = ["", "K", "M", "G", "T", "P", "E", "Z", "Y"]
    base = 1000 if si_units else 1024
    
    unit_index = 0
    size_float = float(size)
    
    while size_float >= base and unit_index < len(units) - 1:
        size_float /= base
        unit_index += 1
    
    if unit_index == 0:
        return str(int(size_float))
    elif size_float >= 10:
        return f"{size_float:.0f}{units[unit_index]}"
    else:
        return f"{size_float:.1f}{units[unit_index]}"


def format_permissions(mode: int) -> str:
    """Format file permissions in ls-style format."""
    perms = ""
    
    # File type
    if stat.S_ISDIR(mode):
        perms += "d"
    elif stat.S_ISLNK(mode):
        perms += "l"
    elif stat.S_ISCHR(mode):
        perms += "c"
    elif stat.S_ISBLK(mode):
        perms += "b"
    elif stat.S_ISSOCK(mode):
        perms += "s"
    elif stat.S_ISFIFO(mode):
        perms += "p"
    else:
        perms += "-"
    
    # Owner permissions
    perms += "r" if mode & stat.S_IRUSR else "-"
    perms += "w" if mode & stat.S_IWUSR else "-"
    if mode & stat.S_ISUID:
        perms += "s" if mode & stat.S_IXUSR else "S"
    else:
        perms += "x" if mode & stat.S_IXUSR else "-"
    
    # Group permissions
    perms += "r" if mode & stat.S_IRGRP else "-"
    perms += "w" if mode & stat.S_IWGRP else "-"
    if mode & stat.S_ISGID:
        perms += "s" if mode & stat.S_IXGRP else "S"
    else:
        perms += "x" if mode & stat.S_IXGRP else "-"
    
    # Other permissions
    perms += "r" if mode & stat.S_IROTH else "-"
    perms += "w" if mode & stat.S_IWOTH else "-"
    if mode & stat.S_ISVTX:
        perms += "t" if mode & stat.S_IXOTH else "T"
    else:
        perms += "x" if mode & stat.S_IXOTH else "-"
    
    return perms
