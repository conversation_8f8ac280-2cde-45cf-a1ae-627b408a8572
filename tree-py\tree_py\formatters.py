"""
Output formatting module for different tree display formats.

This module provides pure functions for formatting tree output in various
formats (Unix, HTML, XML, JSON). All formatters are designed to be
side-effect free and composable.
"""

import json
import html
from pathlib import Path
from typing import TextIO, Optional
from datetime import datetime

from expression.collections import Block
from expression import pipe

from .types import TreeNode, FileInfo, TreeConfig, TreeStats, ColorScheme
from .file_info import format_file_size, format_permissions


class TreeFormatter:
    """Base class for tree formatters using functional principles."""
    
    def __init__(self, config: TreeConfig, color_scheme: Optional[ColorScheme] = None):
        self.config = config
        self.color_scheme = color_scheme or ColorScheme()
    
    def format_tree(self, trees: Block[TreeNode], stats: TreeStats, output: TextIO) -> None:
        """Format and write the complete tree output."""
        self.write_header(output)
        
        for i, tree in enumerate(trees):
            self.format_tree_node(tree, output, "", i == len(trees) - 1)
        
        if not self.config.no_report:
            self.write_footer(stats, output)
    
    def write_header(self, output: TextIO) -> None:
        """Write format-specific header."""
        pass
    
    def write_footer(self, stats: TreeStats, output: TextIO) -> None:
        """Write format-specific footer."""
        pass
    
    def format_tree_node(self, node: TreeNode, output: TextIO, prefix: str, is_last: bool) -> None:
        """Format a single tree node."""
        raise NotImplementedError


class UnixFormatter(TreeFormatter):
    """Unix-style tree formatter with ASCII art."""
    
    def format_tree_node(self, node: TreeNode, output: TextIO, prefix: str, is_last: bool) -> None:
        """Format a tree node in Unix style."""
        match node:
            case TreeNode(tag="file", file=file_info):
                self._write_file_line(file_info, output, prefix, is_last)
            
            case TreeNode(tag="directory", directory=(dir_info, children)):
                self._write_file_line(dir_info, output, prefix, is_last)
                self._format_children(children, output, prefix, is_last)
            
            case TreeNode(tag="error", error=(path, error_msg)):
                self._write_error_line(path, error_msg, output, prefix, is_last)
    
    def _write_file_line(self, file_info: FileInfo, output: TextIO, prefix: str, is_last: bool) -> None:
        """Write a single file line with tree graphics."""
        if not self.config.no_indent:
            # Draw tree structure
            if is_last:
                tree_chars = f"{self.color_scheme.corner}{self.color_scheme.horizontal}{self.color_scheme.horizontal} "
                new_prefix = prefix + f"{self.color_scheme.space * 4}"
            else:
                tree_chars = f"{self.color_scheme.tee}{self.color_scheme.horizontal}{self.color_scheme.horizontal} "
                new_prefix = prefix + f"{self.color_scheme.vertical}{self.color_scheme.space * 3}"
            
            output.write(prefix + tree_chars)
        
        # Format filename with colors
        filename = self._format_filename(file_info)
        
        # Add file information
        info_parts = []
        
        if self.config.show_permissions:
            info_parts.append(f"[{format_permissions(file_info.mode)}]")
        
        if self.config.show_owner:
            info_parts.append(f"[{file_info.uid}]")
        
        if self.config.show_group:
            info_parts.append(f"[{file_info.gid}]")
        
        if self.config.show_size:
            size_str = format_file_size(file_info.size, self.config.human_readable)
            info_parts.append(f"[{size_str}]")
        
        if self.config.show_date:
            date_str = file_info.mtime.strftime("%b %d %H:%M")
            info_parts.append(f"[{date_str}]")
        
        if self.config.show_inode and file_info.inode:
            info_parts.append(f"[{file_info.inode}]")
        
        if self.config.show_device and file_info.device:
            info_parts.append(f"[{file_info.device}]")
        
        # Write the complete line
        if info_parts:
            info_str = " ".join(info_parts)
            output.write(f"{info_str} {filename}")
        else:
            output.write(filename)
        
        # Add link target
        if file_info.is_link and file_info.link_target:
            output.write(f" -> {file_info.link_target}")
        
        # Add error message
        if file_info.error:
            output.write(f" [error: {file_info.error}]")
        
        output.write("\n")
    
    def _write_error_line(self, path: str, error_msg: Optional[str], output: TextIO, prefix: str, is_last: bool) -> None:
        """Write an error line."""
        if not self.config.no_indent:
            if is_last:
                tree_chars = f"{self.color_scheme.corner}{self.color_scheme.horizontal}{self.color_scheme.horizontal} "
            else:
                tree_chars = f"{self.color_scheme.tee}{self.color_scheme.horizontal}{self.color_scheme.horizontal} "
            
            output.write(prefix + tree_chars)
        
        output.write(f"{Path(path).name}")
        if error_msg:
            output.write(f" [error: {error_msg}]")
        output.write("\n")
    
    def _format_filename(self, file_info: FileInfo) -> str:
        """Format filename with appropriate colors."""
        if self.config.no_color or not self._should_use_color():
            return file_info.name
        
        name = file_info.name
        
        if file_info.is_dir:
            return f"{self.color_scheme.directory}{name}{self.color_scheme.reset}"
        elif file_info.is_link:
            return f"{self.color_scheme.link}{name}{self.color_scheme.reset}"
        elif file_info.is_executable:
            return f"{self.color_scheme.executable}{name}{self.color_scheme.reset}"
        elif file_info.is_socket:
            return f"{self.color_scheme.socket}{name}{self.color_scheme.reset}"
        elif file_info.is_fifo:
            return f"{self.color_scheme.fifo}{name}{self.color_scheme.reset}"
        else:
            return name
    
    def _should_use_color(self) -> bool:
        """Determine if colors should be used."""
        if self.config.force_color:
            return True
        if self.config.no_color:
            return False
        
        # Check if output is a terminal
        import sys
        return sys.stdout.isatty()
    
    def _format_children(self, children: Block[TreeNode], output: TextIO, prefix: str, parent_is_last: bool) -> None:
        """Format child nodes."""
        if not children:
            return
        
        # Calculate new prefix
        if parent_is_last:
            new_prefix = prefix + f"{self.color_scheme.space * 4}"
        else:
            new_prefix = prefix + f"{self.color_scheme.vertical}{self.color_scheme.space * 3}"
        
        # Format each child
        for i, child in enumerate(children):
            is_last_child = i == len(children) - 1
            self.format_tree_node(child, output, new_prefix, is_last_child)
    
    def write_footer(self, stats: TreeStats, output: TextIO) -> None:
        """Write Unix-style footer with statistics."""
        output.write("\n")
        
        if stats.total_dirs == 1:
            dir_text = "directory"
        else:
            dir_text = "directories"
        
        if stats.total_files == 1:
            file_text = "file"
        else:
            file_text = "files"
        
        output.write(f"{stats.total_dirs} {dir_text}, {stats.total_files} {file_text}")
        
        if stats.errors > 0:
            output.write(f", {stats.errors} errors")
        
        output.write("\n")


class JsonFormatter(TreeFormatter):
    """JSON tree formatter."""
    
    def format_tree(self, trees: Block[TreeNode], stats: TreeStats, output: TextIO) -> None:
        """Format trees as JSON."""
        tree_data = []
        
        for tree in trees:
            tree_data.append(self._node_to_dict(tree))
        
        result = {
            "trees": tree_data,
            "stats": {
                "directories": stats.total_dirs,
                "files": stats.total_files,
                "size": stats.total_size,
                "errors": stats.errors
            }
        }
        
        json.dump(result, output, indent=2 if not self.config.no_indent else None)
        output.write("\n")
    
    def _node_to_dict(self, node: TreeNode) -> dict:
        """Convert a tree node to a dictionary."""
        match node:
            case TreeNode(tag="file", file=file_info):
                return self._file_info_to_dict(file_info)
            
            case TreeNode(tag="directory", directory=(dir_info, children)):
                result = self._file_info_to_dict(dir_info)
                result["contents"] = [self._node_to_dict(child) for child in children]
                return result
            
            case TreeNode(tag="error", error=(path, error_msg)):
                return {
                    "type": "error",
                    "name": Path(path).name,
                    "path": str(path),
                    "error": error_msg
                }
            
            case _:
                return {"type": "unknown"}
    
    def _file_info_to_dict(self, file_info: FileInfo) -> dict:
        """Convert FileInfo to dictionary."""
        result = {
            "type": "directory" if file_info.is_dir else "file",
            "name": file_info.name,
            "path": str(file_info.path)
        }
        
        if self.config.show_size:
            result["size"] = file_info.size
        
        if self.config.show_permissions:
            result["mode"] = format_permissions(file_info.mode)
        
        if self.config.show_owner:
            result["uid"] = file_info.uid
        
        if self.config.show_group:
            result["gid"] = file_info.gid
        
        if self.config.show_date:
            result["mtime"] = file_info.mtime.isoformat()
        
        if self.config.show_inode and file_info.inode:
            result["inode"] = file_info.inode
        
        if self.config.show_device and file_info.device:
            result["device"] = file_info.device
        
        if file_info.is_link and file_info.link_target:
            result["target"] = file_info.link_target
        
        if file_info.error:
            result["error"] = file_info.error
        
        return result


def create_formatter(config: TreeConfig, color_scheme: Optional[ColorScheme] = None) -> TreeFormatter:
    """Factory function to create the appropriate formatter."""
    if config.output_format == "json":
        return JsonFormatter(config, color_scheme)
    else:
        return UnixFormatter(config, color_scheme)


def format_trees(trees: Block[TreeNode], stats: TreeStats, config: TreeConfig, output: TextIO) -> None:
    """
    Format and output trees using the configured formatter.
    
    This is the main entry point for tree formatting.
    """
    formatter = create_formatter(config)
    formatter.format_tree(trees, stats, output)
