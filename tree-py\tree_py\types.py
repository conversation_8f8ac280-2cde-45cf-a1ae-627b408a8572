"""
Core data types for the tree-py implementation.

This module defines immutable data structures using Expression library's
tagged unions and functional constructs. All types are designed to be
immutable and support functional programming patterns.
"""

from dataclasses import dataclass, field
from pathlib import Path
from typing import Literal, Optional, Union
from datetime import datetime
import stat

from expression import case, tag, tagged_union
from expression.collections import Block, Map


@dataclass(frozen=True)
class FileInfo:
    """Immutable file information record."""
    name: str
    path: Path
    size: int
    mode: int
    uid: int
    gid: int
    atime: datetime
    mtime: datetime
    ctime: datetime
    is_dir: bool
    is_link: bool
    is_executable: bool
    is_socket: bool
    is_fifo: bool
    link_target: Optional[str] = None
    device: Optional[int] = None
    inode: Optional[int] = None
    error: Optional[str] = None


@dataclass(frozen=True)
class TreeConfig:
    """Immutable configuration for tree traversal and output."""
    # Display options
    show_all: bool = False  # -a
    dirs_only: bool = False  # -d
    follow_links: bool = False  # -l
    full_path: bool = False  # -f
    show_hidden: bool = False
    
    # Depth and limits
    max_depth: Optional[int] = None  # -L
    file_limit: Optional[int] = None  # --filelimit
    
    # Sorting options
    sort_by: Literal["name", "version", "size", "mtime", "ctime", "none"] = "name"
    reverse_sort: bool = False  # -r
    dirs_first: bool = False  # --dirsfirst
    files_first: bool = False  # --filesfirst
    
    # Output format
    output_format: Literal["unix", "html", "xml", "json"] = "unix"
    no_indent: bool = False  # -i
    use_ansi: bool = False  # -A
    force_color: bool = False  # -C
    no_color: bool = False  # -n
    
    # File information display
    show_permissions: bool = False  # -p
    show_owner: bool = False  # -u
    show_group: bool = False  # -g
    show_size: bool = False  # -s
    human_readable: bool = False  # -h
    show_date: bool = False  # -D
    show_inode: bool = False  # --inodes
    show_device: bool = False  # --device
    
    # Filtering
    include_patterns: Block[str] = field(default_factory=lambda: Block.empty())  # -P
    exclude_patterns: Block[str] = field(default_factory=lambda: Block.empty())  # -I
    use_gitignore: bool = False  # --gitignore
    ignore_case: bool = False  # --ignore-case
    match_dirs: bool = False  # --matchdirs
    
    # Output options
    output_file: Optional[Path] = None  # -o
    no_report: bool = False  # --noreport
    charset: Optional[str] = None  # --charset
    
    # HTML specific
    html_title: str = "Directory Tree"  # -T
    html_base: Optional[str] = None  # -H


@tagged_union
class TreeNode:
    """Tagged union representing a node in the directory tree."""
    tag: Literal["file", "directory", "error"] = tag()
    
    file: FileInfo = case()
    directory: tuple[FileInfo, Block["TreeNode"]] = case()
    error: tuple[str, Optional[str]] = case()  # (path, error_message)
    
    @staticmethod
    def File(info: FileInfo) -> "TreeNode":
        """Create a file node."""
        return TreeNode(file=info)
    
    @staticmethod
    def Directory(info: FileInfo, children: Block["TreeNode"]) -> "TreeNode":
        """Create a directory node with children."""
        return TreeNode(directory=(info, children))
    
    @staticmethod
    def Error(path: str, error_msg: Optional[str] = None) -> "TreeNode":
        """Create an error node."""
        return TreeNode(error=(path, error_msg))


@tagged_union
class OutputFormat:
    """Tagged union for different output formats."""
    tag: Literal["unix", "html", "xml", "json"] = tag()
    
    unix: dict = case()
    html: dict = case()
    xml: dict = case()
    json: dict = case()


@dataclass(frozen=True)
class TreeStats:
    """Statistics about the tree traversal."""
    total_files: int = 0
    total_dirs: int = 0
    total_size: int = 0
    errors: int = 0


@dataclass(frozen=True)
class ColorScheme:
    """Color scheme for terminal output."""
    directory: str = "\033[01;34m"  # Bold blue
    executable: str = "\033[01;32m"  # Bold green
    link: str = "\033[01;36m"       # Bold cyan
    socket: str = "\033[01;35m"     # Bold magenta
    fifo: str = "\033[33m"          # Yellow
    device: str = "\033[01;33m"     # Bold yellow
    reset: str = "\033[0m"          # Reset
    
    # Tree drawing characters
    vertical: str = "│"
    horizontal: str = "─"
    corner: str = "└"
    tee: str = "├"
    space: str = " "


def file_type_from_mode(mode: int) -> str:
    """Extract file type from mode bits."""
    if stat.S_ISDIR(mode):
        return "directory"
    elif stat.S_ISLNK(mode):
        return "link"
    elif stat.S_ISREG(mode):
        return "file"
    elif stat.S_ISCHR(mode):
        return "char"
    elif stat.S_ISBLK(mode):
        return "block"
    elif stat.S_ISSOCK(mode):
        return "socket"
    elif stat.S_ISFIFO(mode):
        return "fifo"
    else:
        return "unknown"


def is_executable(mode: int) -> bool:
    """Check if file is executable."""
    return bool(mode & (stat.S_IXUSR | stat.S_IXGRP | stat.S_IXOTH))
