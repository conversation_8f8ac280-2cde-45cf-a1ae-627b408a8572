"""
Comprehensive functional tests for tree-py implementation.

This module provides tests that verify the functional programming principles
and correctness of the tree-py implementation.
"""

import tempfile
import os
from pathlib import Path
from datetime import datetime

from expression import Ok, Error
from expression.collections import Block

from tree_py.types import TreeConfig, FileInfo, TreeNode
from tree_py.file_info import create_file_info, list_directory_contents, format_file_size
from tree_py.filters import matches_pattern, parse_gitignore_line, GitignoreRule
from tree_py.sorting import sort_files, compare_by_name, natural_sort_key
from tree_py.tree_builder import build_tree, calculate_tree_stats
from tree_py.config import parse_arguments
from tree_py.main import main


def test_file_info_creation():
    """Test FileInfo creation with functional error handling."""
    print("Testing FileInfo creation...")
    
    # Test with current directory
    current_dir = Path(".")
    config = TreeConfig()
    
    result = create_file_info(current_dir, config)
    match result:
        case Ok(file_info):
            print(f"✓ Successfully created FileInfo for {file_info.name}")
            assert file_info.is_dir
            assert file_info.name == "."
        case Error(error_msg):
            print(f"✗ Error creating FileInfo: {error_msg}")
            assert False, f"Unexpected error: {error_msg}"


def test_pattern_matching():
    """Test pattern matching functionality."""
    print("Testing pattern matching...")
    
    # Test basic patterns
    assert matches_pattern("test.py", "*.py")
    assert matches_pattern("README.md", "README.*")
    assert not matches_pattern("test.txt", "*.py")
    
    # Test case sensitivity
    assert matches_pattern("Test.PY", "*.py", ignore_case=True)
    assert not matches_pattern("Test.PY", "*.py", ignore_case=False)
    
    print("✓ Pattern matching tests passed")


def test_gitignore_parsing():
    """Test gitignore rule parsing."""
    print("Testing gitignore parsing...")
    
    # Test normal pattern
    rule_result = parse_gitignore_line("*.pyc")
    match rule_result:
        case Ok(rule):
            assert rule.original_pattern == "*.pyc"
            assert not rule.is_negation
        case _:
            assert False, "Expected Ok result"
    
    # Test negation pattern
    rule_result = parse_gitignore_line("!important.pyc")
    match rule_result:
        case Ok(rule):
            assert rule.is_negation
        case _:
            assert False, "Expected Ok result"
    
    # Test comment (should return Nothing)
    rule_result = parse_gitignore_line("# This is a comment")
    assert rule_result.is_nothing()
    
    print("✓ Gitignore parsing tests passed")


def test_natural_sorting():
    """Test natural (version) sorting."""
    print("Testing natural sorting...")
    
    # Test natural sort keys
    key1 = natural_sort_key("file1.txt")
    key2 = natural_sort_key("file10.txt")
    key3 = natural_sort_key("file2.txt")
    
    # file1.txt < file2.txt < file10.txt (natural order)
    assert key1 < key3 < key2
    
    print("✓ Natural sorting tests passed")


def test_file_size_formatting():
    """Test file size formatting."""
    print("Testing file size formatting...")
    
    # Test basic formatting
    assert format_file_size(1024) == "1024"
    assert format_file_size(1024, human_readable=True) == "1.0K"
    assert format_file_size(1048576, human_readable=True) == "1.0M"
    
    # Test SI units
    assert format_file_size(1000, human_readable=True, si_units=True) == "1.0K"
    assert format_file_size(1024, human_readable=True, si_units=True) == "1.0K"
    
    print("✓ File size formatting tests passed")


def test_tree_config_immutability():
    """Test that TreeConfig is immutable."""
    print("Testing TreeConfig immutability...")
    
    config = TreeConfig(show_all=True, max_depth=5)
    
    # Verify initial values
    assert config.show_all == True
    assert config.max_depth == 5
    assert config.sort_by == "name"  # default
    
    # TreeConfig is frozen, so this should work for creating new instances
    new_config = TreeConfig(
        show_all=config.show_all,
        max_depth=config.max_depth,
        sort_by="version"
    )
    
    assert new_config.sort_by == "version"
    assert config.sort_by == "name"  # Original unchanged
    
    print("✓ TreeConfig immutability tests passed")


def test_argument_parsing():
    """Test command-line argument parsing."""
    print("Testing argument parsing...")
    
    # Test basic arguments
    result = parse_arguments(["-a", "-L", "2", "."])
    match result:
        case Ok((config, directories)):
            assert config.show_all == True
            assert config.max_depth == 2
            assert len(directories) == 1
            assert directories[0] == Path(".")
        case Error(error_msg):
            assert False, f"Unexpected error: {error_msg}"
    
    # Test JSON output
    result = parse_arguments(["-J", "."])
    match result:
        case Ok((config, directories)):
            assert config.output_format == "json"
        case Error(error_msg):
            assert False, f"Unexpected error: {error_msg}"
    
    print("✓ Argument parsing tests passed")


def create_test_directory_structure():
    """Create a temporary directory structure for testing."""
    temp_dir = Path(tempfile.mkdtemp())
    
    # Create directory structure
    (temp_dir / "dir1").mkdir()
    (temp_dir / "dir1" / "file1.txt").write_text("content1")
    (temp_dir / "dir1" / "file2.py").write_text("print('hello')")
    
    (temp_dir / "dir2").mkdir()
    (temp_dir / "dir2" / "subdir").mkdir()
    (temp_dir / "dir2" / "subdir" / "file3.md").write_text("# Title")
    
    (temp_dir / "file_root.txt").write_text("root content")
    (temp_dir / ".hidden").write_text("hidden content")
    
    # Create .gitignore
    (temp_dir / ".gitignore").write_text("*.pyc\n__pycache__/\n")
    
    return temp_dir


def test_tree_building():
    """Test tree building functionality."""
    print("Testing tree building...")
    
    # Create test directory
    test_dir = create_test_directory_structure()
    
    try:
        # Test basic tree building
        config = TreeConfig()
        result = build_tree(test_dir, config)
        
        match result:
            case Ok(tree):
                print(f"✓ Successfully built tree for {test_dir}")
                
                # Verify it's a directory node
                match tree:
                    case TreeNode(tag="directory", directory=(dir_info, children)):
                        assert dir_info.is_dir
                        assert len(children) > 0
                        print(f"✓ Tree has {len(children)} children")
                    case _:
                        assert False, "Expected directory node"
                
                # Calculate stats
                stats = calculate_tree_stats(tree)
                print(f"✓ Stats: {stats.total_dirs} dirs, {stats.total_files} files")
                
            case Error(error_msg):
                assert False, f"Unexpected error: {error_msg}"
        
        # Test with depth limit
        config_limited = TreeConfig(max_depth=1)
        result_limited = build_tree(test_dir, config_limited)
        
        match result_limited:
            case Ok(tree_limited):
                stats_limited = calculate_tree_stats(tree_limited)
                print(f"✓ Limited depth stats: {stats_limited.total_dirs} dirs, {stats_limited.total_files} files")
            case Error(error_msg):
                assert False, f"Unexpected error: {error_msg}"
    
    finally:
        # Cleanup
        import shutil
        shutil.rmtree(test_dir)


def test_functional_composition():
    """Test functional composition and pipeline operations."""
    print("Testing functional composition...")
    
    from expression import pipe
    from expression.collections import seq
    
    # Create test data
    test_files = [
        FileInfo(
            name="file1.txt", path=Path("file1.txt"), size=100, mode=0o644,
            uid=1000, gid=1000, atime=datetime.now(), mtime=datetime.now(),
            ctime=datetime.now(), is_dir=False, is_link=False, is_executable=False,
            is_socket=False, is_fifo=False
        ),
        FileInfo(
            name="file2.py", path=Path("file2.py"), size=200, mode=0o755,
            uid=1000, gid=1000, atime=datetime.now(), mtime=datetime.now(),
            ctime=datetime.now(), is_dir=False, is_link=False, is_executable=True,
            is_socket=False, is_fifo=False
        ),
        FileInfo(
            name="dir1", path=Path("dir1"), size=0, mode=0o755,
            uid=1000, gid=1000, atime=datetime.now(), mtime=datetime.now(),
            ctime=datetime.now(), is_dir=True, is_link=False, is_executable=False,
            is_socket=False, is_fifo=False
        )
    ]
    
    files_block = Block.of_seq(test_files)
    
    # Test functional pipeline
    result = pipe(
        files_block,
        lambda files: seq.filter(lambda f: not f.is_dir, files),
        lambda files: seq.map(lambda f: f.name, files),
        lambda names: seq.sort(names),
        list
    )
    
    expected = ["file1.txt", "file2.py"]
    assert result == expected
    
    print("✓ Functional composition tests passed")


def test_main_function():
    """Test the main function with various arguments."""
    print("Testing main function...")
    
    # Test help
    exit_code = main(["--help"])
    assert exit_code == 0
    
    # Test version
    exit_code = main(["--version"])
    assert exit_code == 0
    
    # Test basic functionality (should work with current directory)
    exit_code = main(["-L", "1", "."])
    assert exit_code == 0
    
    print("✓ Main function tests passed")


def run_all_tests():
    """Run all tests."""
    print("Running tree-py functional tests...\n")
    
    test_file_info_creation()
    test_pattern_matching()
    test_gitignore_parsing()
    test_natural_sorting()
    test_file_size_formatting()
    test_tree_config_immutability()
    test_argument_parsing()
    test_tree_building()
    test_functional_composition()
    test_main_function()
    
    print("\n✓ All tests passed! The functional implementation is working correctly.")


if __name__ == "__main__":
    run_all_tests()
