"""
Configuration module for command-line argument parsing.

This module provides pure functions for parsing command-line arguments
and creating immutable TreeConfig objects. It follows functional programming
principles with no side effects.
"""

import argparse
import sys
from pathlib import Path
from typing import List, Optional

from expression import Result, Ok, Error
from expression.collections import Block

from .types import TreeConfig


def create_argument_parser() -> argparse.ArgumentParser:
    """Create and configure the argument parser."""
    parser = argparse.ArgumentParser(
        prog="tree-py",
        description="A functional Python implementation of the Unix tree command",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  tree-py                    # Show tree of current directory
  tree-py /path/to/dir       # Show tree of specific directory
  tree-py -a                 # Show all files including hidden
  tree-py -L 2               # Limit depth to 2 levels
  tree-py -I "*.pyc"         # Ignore .pyc files
  tree-py -P "*.py"          # Only show .py files
  tree-py --gitignore        # Use .gitignore files for filtering
  tree-py -J                 # Output in JSON format
        """
    )
    
    # Listing options
    listing_group = parser.add_argument_group("Listing options")
    listing_group.add_argument(
        "-a", "--all",
        action="store_true",
        help="All files are listed (including hidden files)"
    )
    listing_group.add_argument(
        "-d", "--dirs-only",
        action="store_true",
        help="List directories only"
    )
    listing_group.add_argument(
        "-l", "--follow-links",
        action="store_true",
        help="Follow symbolic links like directories"
    )
    listing_group.add_argument(
        "-f", "--full-path",
        action="store_true",
        help="Print the full path prefix for each file"
    )
    listing_group.add_argument(
        "-L", "--level",
        type=int,
        metavar="LEVEL",
        help="Descend only LEVEL directories deep"
    )
    listing_group.add_argument(
        "-P", "--pattern",
        action="append",
        metavar="PATTERN",
        help="List only those files that match the pattern given"
    )
    listing_group.add_argument(
        "-I", "--ignore-pattern",
        action="append",
        metavar="PATTERN",
        help="Do not list files that match the given pattern"
    )
    listing_group.add_argument(
        "--gitignore",
        action="store_true",
        help="Filter by using .gitignore files"
    )
    listing_group.add_argument(
        "--ignore-case",
        action="store_true",
        help="Ignore case when pattern matching"
    )
    listing_group.add_argument(
        "--matchdirs",
        action="store_true",
        help="Include directory names in -P pattern matching"
    )
    listing_group.add_argument(
        "--filelimit",
        type=int,
        metavar="NUM",
        help="Do not descend dirs with more than NUM files in them"
    )
    
    # File options
    file_group = parser.add_argument_group("File options")
    file_group.add_argument(
        "-p", "--permissions",
        action="store_true",
        help="Print the protections for each file"
    )
    file_group.add_argument(
        "-u", "--owner",
        action="store_true",
        help="Displays file owner or UID number"
    )
    file_group.add_argument(
        "-g", "--group",
        action="store_true",
        help="Displays file group owner or GID number"
    )
    file_group.add_argument(
        "-s", "--size",
        action="store_true",
        help="Print the size in bytes of each file"
    )
    file_group.add_argument(
        "-h", "--human-readable",
        action="store_true",
        help="Print the size in a more human readable way"
    )
    file_group.add_argument(
        "-D", "--date",
        action="store_true",
        help="Print the date of last modification"
    )
    file_group.add_argument(
        "--inodes",
        action="store_true",
        help="Print inode number of each file"
    )
    file_group.add_argument(
        "--device",
        action="store_true",
        help="Print device ID number to which each file belongs"
    )
    
    # Sorting options
    sort_group = parser.add_argument_group("Sorting options")
    sort_group.add_argument(
        "-v", "--version-sort",
        action="store_true",
        help="Sort files alphanumerically by version"
    )
    sort_group.add_argument(
        "-t", "--time-sort",
        action="store_true",
        help="Sort files by last modification time"
    )
    sort_group.add_argument(
        "-c", "--ctime-sort",
        action="store_true",
        help="Sort files by last status change time"
    )
    sort_group.add_argument(
        "-U", "--no-sort",
        action="store_true",
        help="Leave files unsorted"
    )
    sort_group.add_argument(
        "-r", "--reverse",
        action="store_true",
        help="Reverse the order of the sort"
    )
    sort_group.add_argument(
        "--dirsfirst",
        action="store_true",
        help="List directories before files"
    )
    sort_group.add_argument(
        "--filesfirst",
        action="store_true",
        help="List files before directories"
    )
    sort_group.add_argument(
        "--sort",
        choices=["name", "version", "size", "mtime", "ctime", "none"],
        help="Select sort type"
    )
    
    # Graphics options
    graphics_group = parser.add_argument_group("Graphics options")
    graphics_group.add_argument(
        "-i", "--no-indent",
        action="store_true",
        help="Don't print indentation lines"
    )
    graphics_group.add_argument(
        "-A", "--ansi",
        action="store_true",
        help="Print ANSI lines graphic indentation lines"
    )
    graphics_group.add_argument(
        "-n", "--no-color",
        action="store_true",
        help="Turn colorization off always"
    )
    graphics_group.add_argument(
        "-C", "--color",
        action="store_true",
        help="Turn colorization on always"
    )
    
    # Output options
    output_group = parser.add_argument_group("Output options")
    output_group.add_argument(
        "-J", "--json",
        action="store_true",
        help="Prints out a JSON representation of the tree"
    )
    output_group.add_argument(
        "-o", "--output",
        metavar="FILE",
        help="Output to file instead of stdout"
    )
    output_group.add_argument(
        "--noreport",
        action="store_true",
        help="Turn off file/directory count at end of tree listing"
    )
    output_group.add_argument(
        "--charset",
        metavar="CHARSET",
        help="Use charset for terminal output"
    )
    
    # Positional arguments
    parser.add_argument(
        "directories",
        nargs="*",
        default=["."],
        help="Directories to list (default: current directory)"
    )
    
    return parser


def validate_arguments(args: argparse.Namespace) -> Result[None, str]:
    """Validate parsed arguments for consistency."""
    errors = []
    
    # Check for conflicting sort options
    sort_options = [args.version_sort, args.time_sort, args.ctime_sort, args.no_sort]
    if sum(sort_options) > 1:
        errors.append("Only one sort option can be specified")
    
    # Check for conflicting directory/file first options
    if args.dirsfirst and args.filesfirst:
        errors.append("Cannot specify both --dirsfirst and --filesfirst")
    
    # Check for conflicting color options
    if args.color and args.no_color:
        errors.append("Cannot specify both --color and --no-color")
    
    # Validate level
    if args.level is not None and args.level < 0:
        errors.append("Level must be non-negative")
    
    # Validate filelimit
    if args.filelimit is not None and args.filelimit < 0:
        errors.append("File limit must be non-negative")
    
    # Check if directories exist
    for directory in args.directories:
        path = Path(directory)
        if not path.exists():
            errors.append(f"Directory does not exist: {directory}")
    
    if errors:
        return Error("; ".join(errors))
    
    return Ok(None)


def args_to_config(args: argparse.Namespace) -> TreeConfig:
    """Convert parsed arguments to TreeConfig."""
    # Determine sort type
    sort_by = "name"  # default
    if args.sort:
        sort_by = args.sort
    elif args.version_sort:
        sort_by = "version"
    elif args.time_sort:
        sort_by = "mtime"
    elif args.ctime_sort:
        sort_by = "ctime"
    elif args.no_sort:
        sort_by = "none"
    
    # Determine output format
    output_format = "unix"
    if args.json:
        output_format = "json"
    
    # Convert patterns to Block
    include_patterns = Block.of_seq(args.pattern or [])
    exclude_patterns = Block.of_seq(args.ignore_pattern or [])
    
    # Handle human readable size (implies show size)
    show_size = args.size or args.human_readable
    
    return TreeConfig(
        # Display options
        show_all=args.all,
        dirs_only=args.dirs_only,
        follow_links=args.follow_links,
        full_path=args.full_path,
        
        # Depth and limits
        max_depth=args.level,
        file_limit=args.filelimit,
        
        # Sorting options
        sort_by=sort_by,
        reverse_sort=args.reverse,
        dirs_first=args.dirsfirst,
        files_first=args.filesfirst,
        
        # Output format
        output_format=output_format,
        no_indent=args.no_indent,
        use_ansi=args.ansi,
        force_color=args.color,
        no_color=args.no_color,
        
        # File information display
        show_permissions=args.permissions,
        show_owner=args.owner,
        show_group=args.group,
        show_size=show_size,
        human_readable=args.human_readable,
        show_date=args.date,
        show_inode=args.inodes,
        show_device=args.device,
        
        # Filtering
        include_patterns=include_patterns,
        exclude_patterns=exclude_patterns,
        use_gitignore=args.gitignore,
        ignore_case=args.ignore_case,
        match_dirs=args.matchdirs,
        
        # Output options
        output_file=Path(args.output) if args.output else None,
        no_report=args.noreport,
        charset=args.charset
    )


def parse_arguments(argv: Optional[List[str]] = None) -> Result[tuple[TreeConfig, List[Path]], str]:
    """
    Parse command-line arguments and return configuration and directory paths.
    
    This is the main entry point for argument parsing.
    """
    parser = create_argument_parser()
    
    try:
        args = parser.parse_args(argv)
    except SystemExit as e:
        if e.code == 0:
            # Help was displayed
            return Error("Help displayed")
        else:
            return Error("Invalid arguments")
    
    # Validate arguments
    validation_result = validate_arguments(args)
    match validation_result:
        case Error(error_msg):
            return Error(error_msg)
        case Ok(_):
            pass
    
    # Convert to config
    config = args_to_config(args)
    
    # Convert directory strings to Path objects
    directories = [Path(d) for d in args.directories]
    
    return Ok((config, directories))


def print_version() -> None:
    """Print version information."""
    from . import __version__
    print(f"tree-py {__version__}")
    print("A functional Python implementation of the Unix tree command")


def print_help() -> None:
    """Print help information."""
    parser = create_argument_parser()
    parser.print_help()
