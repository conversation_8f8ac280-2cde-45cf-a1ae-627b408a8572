"""
Sorting module for file and directory ordering.

This module provides pure functions for sorting files and directories
according to various criteria. All sorting functions are designed to be
composable and side-effect free.
"""

import re
from typing import Callable, Any
from functools import cmp_to_key

from expression.collections import Block, seq
from expression import pipe

from .types import FileInfo, TreeConfig


def natural_sort_key(text: str) -> list[Any]:
    """
    Generate a key for natural (version) sorting.
    
    This splits the string into text and numeric parts, converting
    numeric parts to integers for proper numerical comparison.
    """
    def convert(part: str) -> Any:
        if part.isdigit():
            return int(part)
        return part.lower()
    
    return [convert(part) for part in re.split(r'(\d+)', text)]


def compare_by_name(a: FileInfo, b: FileInfo) -> int:
    """Compare two FileInfo objects by name (case-insensitive)."""
    name_a = a.name.lower()
    name_b = b.name.lower()
    
    if name_a < name_b:
        return -1
    elif name_a > name_b:
        return 1
    else:
        return 0


def compare_by_version(a: FileInfo, b: FileInfo) -> int:
    """Compare two FileInfo objects by version (natural sort)."""
    key_a = natural_sort_key(a.name)
    key_b = natural_sort_key(b.name)
    
    if key_a < key_b:
        return -1
    elif key_a > key_b:
        return 1
    else:
        return 0


def compare_by_size(a: FileInfo, b: FileInfo) -> int:
    """Compare two FileInfo objects by size."""
    if a.size < b.size:
        return -1
    elif a.size > b.size:
        return 1
    else:
        return compare_by_name(a, b)  # Secondary sort by name


def compare_by_mtime(a: FileInfo, b: FileInfo) -> int:
    """Compare two FileInfo objects by modification time."""
    if a.mtime < b.mtime:
        return -1
    elif a.mtime > b.mtime:
        return 1
    else:
        return compare_by_name(a, b)  # Secondary sort by name


def compare_by_ctime(a: FileInfo, b: FileInfo) -> int:
    """Compare two FileInfo objects by creation/change time."""
    if a.ctime < b.ctime:
        return -1
    elif a.ctime > b.ctime:
        return 1
    else:
        return compare_by_name(a, b)  # Secondary sort by name


def get_comparator(sort_by: str) -> Callable[[FileInfo, FileInfo], int]:
    """Get the appropriate comparator function for the given sort type."""
    comparators = {
        "name": compare_by_name,
        "version": compare_by_version,
        "size": compare_by_size,
        "mtime": compare_by_mtime,
        "ctime": compare_by_ctime,
    }
    
    return comparators.get(sort_by, compare_by_name)


def reverse_comparator(comparator: Callable[[FileInfo, FileInfo], int]) -> Callable[[FileInfo, FileInfo], int]:
    """Reverse the order of a comparator function."""
    def reversed_compare(a: FileInfo, b: FileInfo) -> int:
        return -comparator(a, b)
    
    return reversed_compare


def dirs_first_comparator(base_comparator: Callable[[FileInfo, FileInfo], int]) -> Callable[[FileInfo, FileInfo], int]:
    """Modify a comparator to sort directories before files."""
    def dirs_first_compare(a: FileInfo, b: FileInfo) -> int:
        # If one is a directory and the other isn't, directory comes first
        if a.is_dir and not b.is_dir:
            return -1
        elif not a.is_dir and b.is_dir:
            return 1
        else:
            # Both are directories or both are files, use base comparator
            return base_comparator(a, b)
    
    return dirs_first_compare


def files_first_comparator(base_comparator: Callable[[FileInfo, FileInfo], int]) -> Callable[[FileInfo, FileInfo], int]:
    """Modify a comparator to sort files before directories."""
    def files_first_compare(a: FileInfo, b: FileInfo) -> int:
        # If one is a file and the other is a directory, file comes first
        if not a.is_dir and b.is_dir:
            return -1
        elif a.is_dir and not b.is_dir:
            return 1
        else:
            # Both are directories or both are files, use base comparator
            return base_comparator(a, b)
    
    return files_first_compare


def create_comparator(config: TreeConfig) -> Callable[[FileInfo, FileInfo], int]:
    """
    Create a comparator function based on the configuration.
    
    This function composes the various sorting options into a single
    comparator function.
    """
    # Start with base comparator
    if config.sort_by == "none":
        # No sorting - maintain original order
        return lambda a, b: 0
    
    base_comparator = get_comparator(config.sort_by)
    
    # Apply directory/file ordering
    if config.dirs_first:
        comparator = dirs_first_comparator(base_comparator)
    elif config.files_first:
        comparator = files_first_comparator(base_comparator)
    else:
        comparator = base_comparator
    
    # Apply reverse ordering
    if config.reverse_sort:
        comparator = reverse_comparator(comparator)
    
    return comparator


def sort_files(files: Block[FileInfo], config: TreeConfig) -> Block[FileInfo]:
    """
    Sort a block of FileInfo objects according to the configuration.
    
    This is a pure function that returns a new sorted Block without
    modifying the original.
    """
    if config.sort_by == "none":
        return files  # No sorting
    
    comparator = create_comparator(config)
    
    # Convert to list, sort, and convert back to Block
    files_list = list(files)
    files_list.sort(key=cmp_to_key(comparator))
    
    return Block.of_seq(files_list)


def partition_by_type(files: Block[FileInfo]) -> tuple[Block[FileInfo], Block[FileInfo]]:
    """
    Partition files into directories and non-directories.
    
    Returns a tuple of (directories, files).
    """
    directories = []
    regular_files = []
    
    for file_info in files:
        if file_info.is_dir:
            directories.append(file_info)
        else:
            regular_files.append(file_info)
    
    return Block.of_seq(directories), Block.of_seq(regular_files)


def sort_with_type_grouping(files: Block[FileInfo], config: TreeConfig) -> Block[FileInfo]:
    """
    Sort files with explicit type grouping.
    
    This function first partitions files by type, sorts each group separately,
    then combines them according to the configuration.
    """
    if config.sort_by == "none":
        return files
    
    directories, regular_files = partition_by_type(files)
    
    # Sort each group separately
    sorted_directories = sort_files(directories, config)
    sorted_files = sort_files(regular_files, config)
    
    # Combine according to configuration
    if config.dirs_first:
        return pipe(
            [sorted_directories, sorted_files],
            seq.concat,
            Block.of_seq
        )
    elif config.files_first:
        return pipe(
            [sorted_files, sorted_directories],
            seq.concat,
            Block.of_seq
        )
    else:
        # Mixed sorting - combine and sort again
        combined = pipe(
            [sorted_directories, sorted_files],
            seq.concat,
            Block.of_seq
        )
        return sort_files(combined, config)


def stable_sort_files(files: Block[FileInfo], config: TreeConfig) -> Block[FileInfo]:
    """
    Perform a stable sort of files.
    
    This ensures that files with equal sort keys maintain their relative order.
    """
    if config.sort_by == "none":
        return files
    
    # Python's sort is stable by default
    return sort_files(files, config)


def multi_level_sort(files: Block[FileInfo], sort_keys: list[str], reverse: bool = False) -> Block[FileInfo]:
    """
    Sort files by multiple criteria in order of priority.
    
    Args:
        files: Block of FileInfo objects to sort
        sort_keys: List of sort criteria in order of priority
        reverse: Whether to reverse the final sort order
    """
    if not sort_keys:
        return files
    
    files_list = list(files)
    
    # Sort by each key in reverse order of priority (stable sort)
    for sort_key in reversed(sort_keys):
        comparator = get_comparator(sort_key)
        if reverse:
            comparator = reverse_comparator(comparator)
        files_list.sort(key=cmp_to_key(comparator))
    
    return Block.of_seq(files_list)
