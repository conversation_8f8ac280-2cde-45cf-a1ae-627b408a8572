[project]
name = "tree-py"
version = "0.1.0"
description = "A production-quality functional Python implementation of the Unix tree command"
readme = "README.md"
requires-python = ">=3.11"
dependencies = [
    "expression>=5.6.0",
]

[project.scripts]
tree-py = "tree_py.main:main"

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.mypy]
python_version = "3.11"
strict = true
warn_return_any = true
warn_unused_configs = true
