"""
Main entry point for the tree-py application.

This module provides the main function that orchestrates the entire tree
generation process using functional composition and the Expression library's
monadic error handling.
"""

import sys
from pathlib import Path
from typing import TextIO, Optional, Generator

from expression import Result, Ok, Error, effect, pipe
from expression.collections import Block

from .config import parse_arguments, print_version, print_help
from .tree_builder import build_multiple_trees, calculate_multiple_trees_stats
from .formatters import format_trees
from .types import TreeConfig, TreeNode, TreeStats


def open_output_file(output_path: Optional[Path]) -> Result[TextIO, str]:
    """
    Open output file or return stdout.
    
    Returns a Result containing either a file handle or an error message.
    """
    if output_path is None:
        return Ok(sys.stdout)
    
    try:
        file_handle = open(output_path, 'w', encoding='utf-8')
        return Ok(file_handle)
    except (OSError, IOError) as e:
        return Error(f"Cannot open output file {output_path}: {e}")


def close_output_file(file_handle: TextIO) -> None:
    """Close output file if it's not stdout."""
    if file_handle != sys.stdout:
        file_handle.close()


def run_tree_command(
    config: TreeConfig,
    directories: list[Path]
) -> Result[int, str]:
    """
    Run the tree command with the given configuration and directories.

    This function uses the Result type for error handling,
    ensuring that any error in the pipeline is properly handled.

    Returns:
        Result[int, str]: Exit code (0 for success) or error message
    """
    # Open output file
    output_file_result = open_output_file(config.output_file)

    if output_file_result.is_error():
        return output_file_result

    output_file = output_file_result.value

    try:
        # Build trees for all directories
        trees_result = build_multiple_trees(directories, config)

        if trees_result.is_error():
            return trees_result

        trees = trees_result.value

        # Calculate statistics
        stats = calculate_multiple_trees_stats(trees)

        # Format and output trees
        format_trees(trees, stats, config, output_file)

        # Return success exit code
        return Ok(0)

    finally:
        # Always close the output file
        close_output_file(output_file)


def handle_special_arguments(argv: list[str]) -> Optional[int]:
    """
    Handle special arguments like --version and --help.
    
    Returns exit code if special argument was handled, None otherwise.
    """
    if "--version" in argv:
        print_version()
        return 0
    
    if "--help" in argv or "-h" in argv:
        print_help()
        return 0
    
    return None


def main(argv: Optional[list[str]] = None) -> int:
    """
    Main entry point for the tree-py application.
    
    This function orchestrates the entire application flow using functional
    composition and monadic error handling.
    
    Args:
        argv: Command line arguments (defaults to sys.argv[1:])
    
    Returns:
        int: Exit code (0 for success, non-zero for error)
    """
    if argv is None:
        argv = sys.argv[1:]
    
    # Handle special arguments first
    special_exit_code = handle_special_arguments(argv)
    if special_exit_code is not None:
        return special_exit_code
    
    # Parse command line arguments
    parse_result = parse_arguments(argv)

    if parse_result.is_error():
        error_msg = parse_result.error
        if error_msg == "Help displayed":
            return 0
        else:
            print(f"tree-py: {error_msg}", file=sys.stderr)
            return 1

    config, directories = parse_result.value

    # Run the tree command
    run_result = run_tree_command(config, directories)

    if run_result.is_ok():
        return run_result.value
    else:
        print(f"tree-py: {run_result.error}", file=sys.stderr)
        return 2


def main_with_error_handling() -> int:
    """
    Main entry point with comprehensive error handling.
    
    This wrapper catches any unexpected exceptions and provides
    a clean error message.
    """
    try:
        return main()
    except KeyboardInterrupt:
        print("\ntree-py: Interrupted by user", file=sys.stderr)
        return 130
    except Exception as e:
        print(f"tree-py: Unexpected error: {e}", file=sys.stderr)
        return 3


# Functional composition helpers
def compose_tree_pipeline(config: TreeConfig, directories: list[Path]) -> Result[tuple[Block[TreeNode], TreeStats], str]:
    """
    Compose the tree building pipeline using functional composition.
    
    This function demonstrates how to use Expression's pipe function
    for creating clean, readable pipelines.
    """
    return pipe(
        directories,
        lambda dirs: build_multiple_trees(dirs, config),
        lambda trees_result: trees_result.map(
            lambda trees: (trees, calculate_multiple_trees_stats(trees))
        )
    )


def create_tree_processor(config: TreeConfig) -> callable:
    """
    Create a tree processor function configured with the given config.
    
    This demonstrates function composition and partial application
    using functional programming principles.
    """
    def process_directories(directories: list[Path]) -> Result[int, str]:
        """Process directories with the configured settings."""
        return run_tree_command(config, directories)
    
    return process_directories


# Example of using the functional API
def functional_tree_example():
    """
    Example of using the tree-py functionality in a functional style.
    
    This demonstrates how the library can be used programmatically
    with functional composition.
    """
    from .types import TreeConfig
    
    # Create configuration functionally
    config = TreeConfig(
        show_all=True,
        max_depth=2,
        sort_by="name",
        output_format="json"
    )
    
    # Create processor
    processor = create_tree_processor(config)
    
    # Process directories
    directories = [Path(".")]
    result = processor(directories)
    
    match result:
        case Ok(exit_code):
            print(f"Success: {exit_code}")
        case Error(error_msg):
            print(f"Error: {error_msg}")


if __name__ == "__main__":
    sys.exit(main_with_error_handling())
