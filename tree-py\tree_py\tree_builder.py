"""
Tree building module using functional programming principles.

This module provides pure functions for building directory trees recursively.
It uses the Result and Option types for error handling and the Expression
library's functional constructs for composition.
"""

from pathlib import Path
from typing import Generator, Set

from expression import Result, Ok, Error, effect
from expression.collections import Block, seq
from expression import pipe

from .types import TreeNode, FileInfo, TreeConfig, TreeStats
from .file_info import create_file_info, list_directory_contents
from .sorting import sort_files
from .filters import apply_filters, should_show_file


def track_visited_inodes(visited: Set[tuple[int, int]], file_info: FileInfo) -> bool:
    """
    Track visited inodes to detect circular references.
    
    Returns True if this inode has been visited before (circular reference).
    """
    if file_info.inode is None or file_info.device is None:
        return False
    
    inode_key = (file_info.device, file_info.inode)
    if inode_key in visited:
        return True
    
    visited.add(inode_key)
    return False


def build_tree_recursive(
    path: Path,
    config: TreeConfig,
    current_depth: int = 0,
    visited_inodes: Set[tuple[int, int]] = None,
    base_path: Path = None
) -> Result[TreeNode, str]:
    """
    Recursively build a directory tree using Result type for error handling.

    This function uses the Result type to handle errors gracefully throughout
    the tree building process.
    """
    if visited_inodes is None:
        visited_inodes = set()

    if base_path is None:
        base_path = path

    # Check depth limit
    if config.max_depth is not None and current_depth >= config.max_depth:
        # Create a simple file node without recursing
        file_info_result = create_file_info(path, config)
        if file_info_result.is_error():
            return file_info_result
        return Ok(TreeNode.File(file_info_result.value))

    # Get file information
    file_info_result = create_file_info(path, config)
    if file_info_result.is_error():
        return file_info_result

    file_info = file_info_result.value

    # Check if we should show this file
    if not should_show_file(file_info, config):
        return Ok(TreeNode.Error(str(path), "Filtered out"))

    # If it's not a directory, return a file node
    if not file_info.is_dir:
        return Ok(TreeNode.File(file_info))

    # Check for circular references (symbolic links)
    if file_info.is_link and track_visited_inodes(visited_inodes, file_info):
        return Ok(TreeNode.Error(str(path), "Circular reference detected"))

    # List directory contents
    contents_result = list_directory_contents(path, config)
    if contents_result.is_error():
        return Ok(TreeNode.Error(str(path), contents_result.error))

    contents = contents_result.value

    # Apply filters
    filtered_contents = apply_filters(contents, config, base_path)

    # Check file limit
    if config.file_limit is not None and len(filtered_contents) > config.file_limit:
        return Ok(TreeNode.Error(str(path), f"Directory has {len(filtered_contents)} files, exceeds limit of {config.file_limit}"))

    # Sort contents
    sorted_contents = sort_files(filtered_contents, config)

    # Recursively build child nodes
    children = []
    for child_info in sorted_contents:
        child_result = build_tree_recursive(
            child_info.path,
            config,
            current_depth + 1,
            visited_inodes.copy(),  # Pass a copy to avoid mutation
            base_path
        )

        if child_result.is_ok():
            children.append(child_result.value)
        else:
            # Create error node for failed children
            error_node = TreeNode.Error(str(child_info.path), child_result.error)
            children.append(error_node)

    return Ok(TreeNode.Directory(file_info, Block.of_seq(children)))


def build_tree(path: Path, config: TreeConfig) -> Result[TreeNode, str]:
    """
    Build a directory tree starting from the given path.
    
    This is the main entry point for tree building.
    """
    if not path.exists():
        return Error(f"Path does not exist: {path}")
    
    return build_tree_recursive(path, config)


def build_multiple_trees(paths: list[Path], config: TreeConfig) -> Result[Block[TreeNode], str]:
    """
    Build trees for multiple root paths.
    
    This function builds separate trees for each path and returns them as a Block.
    """
    trees = []
    errors = []
    
    for path in paths:
        tree_result = build_tree(path, config)
        match tree_result:
            case Ok(tree):
                trees.append(tree)
            case Error(error_msg):
                errors.append(f"{path}: {error_msg}")
    
    if errors and not trees:
        return Error("; ".join(errors))
    
    return Ok(Block.of_seq(trees))


def calculate_tree_stats(tree: TreeNode) -> TreeStats:
    """
    Calculate statistics for a tree.
    
    This function recursively traverses the tree to count files, directories,
    and calculate total size.
    """
    def calculate_stats_recursive(node: TreeNode, stats: TreeStats) -> TreeStats:
        match node:
            case TreeNode(tag="file", file=file_info):
                return TreeStats(
                    total_files=stats.total_files + 1,
                    total_dirs=stats.total_dirs,
                    total_size=stats.total_size + file_info.size,
                    errors=stats.errors
                )
            
            case TreeNode(tag="directory", directory=(dir_info, children)):
                # Count the directory itself
                dir_stats = TreeStats(
                    total_files=stats.total_files,
                    total_dirs=stats.total_dirs + 1,
                    total_size=stats.total_size,
                    errors=stats.errors
                )
                
                # Recursively calculate stats for children
                for child in children:
                    dir_stats = calculate_stats_recursive(child, dir_stats)
                
                return dir_stats
            
            case TreeNode(tag="error", error=(path, error_msg)):
                return TreeStats(
                    total_files=stats.total_files,
                    total_dirs=stats.total_dirs,
                    total_size=stats.total_size,
                    errors=stats.errors + 1
                )
            
            case _:
                return stats
    
    return calculate_stats_recursive(tree, TreeStats())


def calculate_multiple_trees_stats(trees: Block[TreeNode]) -> TreeStats:
    """Calculate combined statistics for multiple trees."""
    total_stats = TreeStats()
    
    for tree in trees:
        tree_stats = calculate_tree_stats(tree)
        total_stats = TreeStats(
            total_files=total_stats.total_files + tree_stats.total_files,
            total_dirs=total_stats.total_dirs + tree_stats.total_dirs,
            total_size=total_stats.total_size + tree_stats.total_size,
            errors=total_stats.errors + tree_stats.errors
        )
    
    return total_stats


def prune_empty_directories(tree: TreeNode) -> TreeNode:
    """
    Remove empty directories from the tree.
    
    This function recursively removes directories that contain no files
    (only empty subdirectories).
    """
    match tree:
        case TreeNode(tag="file", file=file_info):
            return tree  # Files are always kept
        
        case TreeNode(tag="directory", directory=(dir_info, children)):
            # Recursively prune children
            pruned_children = []
            for child in children:
                pruned_child = prune_empty_directories(child)
                
                # Keep the child if it's a file or a non-empty directory
                match pruned_child:
                    case TreeNode(tag="file"):
                        pruned_children.append(pruned_child)
                    case TreeNode(tag="directory", directory=(_, child_children)):
                        if child_children:  # Directory has children
                            pruned_children.append(pruned_child)
                    case TreeNode(tag="error"):
                        pruned_children.append(pruned_child)  # Keep errors
            
            return TreeNode.Directory(dir_info, Block.of_seq(pruned_children))
        
        case TreeNode(tag="error"):
            return tree  # Keep error nodes
        
        case _:
            return tree


def filter_tree_by_depth(tree: TreeNode, max_depth: int, current_depth: int = 0) -> TreeNode:
    """
    Filter a tree to only include nodes up to a certain depth.
    
    This is useful for implementing the -L option after tree construction.
    """
    if current_depth >= max_depth:
        # Convert directories to files at the depth limit
        match tree:
            case TreeNode(tag="directory", directory=(dir_info, _)):
                return TreeNode.File(dir_info)
            case _:
                return tree
    
    match tree:
        case TreeNode(tag="directory", directory=(dir_info, children)):
            filtered_children = []
            for child in children:
                filtered_child = filter_tree_by_depth(child, max_depth, current_depth + 1)
                filtered_children.append(filtered_child)
            
            return TreeNode.Directory(dir_info, Block.of_seq(filtered_children))
        
        case _:
            return tree


def tree_to_paths(tree: TreeNode, include_directories: bool = True) -> Block[Path]:
    """
    Extract all file paths from a tree.
    
    This function flattens the tree structure into a list of paths.
    """
    def collect_paths(node: TreeNode, paths: list[Path]) -> None:
        match node:
            case TreeNode(tag="file", file=file_info):
                paths.append(file_info.path)
            
            case TreeNode(tag="directory", directory=(dir_info, children)):
                if include_directories:
                    paths.append(dir_info.path)
                
                for child in children:
                    collect_paths(child, paths)
            
            case TreeNode(tag="error", error=(path, _)):
                paths.append(Path(path))
    
    paths = []
    collect_paths(tree, paths)
    return Block.of_seq(paths)
