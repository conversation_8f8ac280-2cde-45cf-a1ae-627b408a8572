# Tree-py: Functional Python Implementation of Unix Tree Command

A production-quality Python implementation of the Unix `tree` command built using **strict functional programming principles** with the [Expression](https://github.com/dbrattli/Expression) library.

## 🎯 Project Overview

This project demonstrates how to implement a complex command-line utility using pure functional programming in Python. It serves as both a practical tool and an educational example of functional programming concepts.

### Key Features

- **🔧 Complete Feature Parity**: Implements all major features of the original Unix `tree` command
- **🧮 Pure Functional Programming**: No classes, methods, or inheritance - only pure functions
- **🚄 Railway-Oriented Programming**: Uses `Result` and `Option` types for elegant error handling
- **🔄 Immutable Data Structures**: All data types are immutable using Expression's collections
- **🧩 Function Composition**: Extensive use of `pipe` and `compose` for readable code
- **⚡ Monadic Effects**: Uses Expression's `effect` decorators for side-effect management
- **🎨 Multiple Output Formats**: Unix, JSON, XML, and HTML output formats
- **🔍 Advanced Filtering**: Pattern matching, gitignore support, and custom filters
- **📊 Comprehensive Sorting**: Multiple sorting criteria with functional composition

## 🏗️ Functional Architecture

### Core Principles Applied

1. **Immutability**: All data structures are immutable
2. **Pure Functions**: No side effects except at I/O boundaries
3. **Function Composition**: Complex operations built from simple functions
4. **Monadic Error Handling**: `Result` and `Option` types eliminate exceptions
5. **Railway-Oriented Programming**: Elegant error propagation through pipelines

### Module Structure

```
tree_py/
├── types.py           # Immutable data types and tagged unions
├── file_info.py       # File system operations with Result types
├── filters.py         # Pattern matching and gitignore support
├── sorting.py         # Pure sorting functions
├── tree_builder.py    # Recursive tree construction
├── formatters.py      # Output formatting (Unix, JSON, etc.)
├── config.py          # Command-line parsing
└── main.py           # Main entry point with effect composition
```