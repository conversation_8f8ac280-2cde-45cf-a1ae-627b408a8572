"""
Filtering module for pattern matching and gitignore support.

This module provides pure functions for filtering files based on patterns,
gitignore rules, and other criteria. All functions are designed to be
side-effect free and composable.
"""

import fnmatch
import re
from pathlib import Path
from typing import Optional

from expression import Result, Ok, Error, Option, Some, Nothing
from expression.collections import Block, Seq, seq
from expression import pipe

from .types import FileInfo, TreeConfig


def normalize_pattern(pattern: str, ignore_case: bool = False) -> str:
    """Normalize a pattern for matching."""
    if ignore_case:
        return pattern.lower()
    return pattern


def normalize_name(name: str, ignore_case: bool = False) -> str:
    """Normalize a filename for matching."""
    if ignore_case:
        return name.lower()
    return name


def matches_pattern(name: str, pattern: str, ignore_case: bool = False) -> bool:
    """
    Check if a filename matches a pattern using fnmatch.
    
    Supports shell-style wildcards:
    - * matches everything
    - ? matches any single character
    - [seq] matches any character in seq
    - [!seq] matches any character not in seq
    """
    normalized_name = normalize_name(name, ignore_case)
    normalized_pattern = normalize_pattern(pattern, ignore_case)
    
    return fnmatch.fnmatch(normalized_name, normalized_pattern)


def matches_any_pattern(name: str, patterns: Block[str], ignore_case: bool = False) -> bool:
    """Check if a filename matches any of the given patterns."""
    return pipe(
        patterns,
        seq.exists(lambda pattern: matches_pattern(name, pattern, ignore_case))
    )


def matches_all_patterns(name: str, patterns: Block[str], ignore_case: bool = False) -> bool:
    """Check if a filename matches all of the given patterns."""
    return pipe(
        patterns,
        seq.for_all(lambda pattern: matches_pattern(name, pattern, ignore_case))
    )


class GitignoreRule:
    """Represents a single gitignore rule."""
    
    def __init__(self, pattern: str, is_negation: bool = False, is_directory_only: bool = False):
        self.original_pattern = pattern
        self.is_negation = is_negation
        self.is_directory_only = is_directory_only
        self.regex = self._compile_pattern(pattern)
    
    def _compile_pattern(self, pattern: str) -> re.Pattern[str]:
        """Compile gitignore pattern to regex."""
        # Remove leading/trailing whitespace
        pattern = pattern.strip()
        
        # Handle negation
        if pattern.startswith('!'):
            self.is_negation = True
            pattern = pattern[1:]
        
        # Handle directory-only patterns
        if pattern.endswith('/'):
            self.is_directory_only = True
            pattern = pattern[:-1]
        
        # Escape special regex characters except for gitignore wildcards
        pattern = re.escape(pattern)
        
        # Convert gitignore wildcards to regex
        pattern = pattern.replace(r'\*\*', '.*')  # ** matches any number of directories
        pattern = pattern.replace(r'\*', '[^/]*')  # * matches anything except /
        pattern = pattern.replace(r'\?', '[^/]')   # ? matches any single character except /
        
        # Handle leading slash (absolute path from repo root)
        if pattern.startswith('/'):
            pattern = '^' + pattern[1:]
        else:
            pattern = '(^|/)' + pattern
        
        # Handle trailing patterns
        pattern += '(/.*)?$'
        
        return re.compile(pattern)
    
    def matches(self, path: str, is_directory: bool = False) -> bool:
        """Check if this rule matches the given path."""
        if self.is_directory_only and not is_directory:
            return False
        
        return bool(self.regex.search(path))


def parse_gitignore_line(line: str) -> Option[GitignoreRule]:
    """Parse a single line from a gitignore file."""
    line = line.strip()
    
    # Skip empty lines and comments
    if not line or line.startswith('#'):
        return Nothing
    
    return Some(GitignoreRule(line))


def parse_gitignore_file(gitignore_path: Path) -> Result[Block[GitignoreRule], str]:
    """Parse a gitignore file and return rules."""
    try:
        if not gitignore_path.exists():
            return Ok(Block.empty())
        
        with open(gitignore_path, 'r', encoding='utf-8', errors='ignore') as f:
            lines = f.readlines()
        
        rules = []
        for line in lines:
            rule_option = parse_gitignore_line(line)
            match rule_option:
                case Some(rule):
                    rules.append(rule)
                case Nothing:
                    pass
        
        return Ok(Block.of_seq(rules))
    
    except (OSError, IOError) as e:
        return Error(f"Cannot read gitignore file {gitignore_path}: {e}")


def find_gitignore_files(start_path: Path) -> Block[Path]:
    """Find all gitignore files from start_path up to the root."""
    gitignore_files = []
    current_path = start_path.resolve()
    
    while True:
        gitignore_path = current_path / '.gitignore'
        if gitignore_path.exists():
            gitignore_files.append(gitignore_path)
        
        parent = current_path.parent
        if parent == current_path:  # Reached root
            break
        current_path = parent
    
    return Block.of_seq(reversed(gitignore_files))  # Root to leaf order


def is_ignored_by_gitignore(file_path: Path, gitignore_rules: Block[GitignoreRule]) -> bool:
    """
    Check if a file is ignored by gitignore rules.
    
    Rules are applied in order, with later rules overriding earlier ones.
    Negation rules (starting with !) can un-ignore files.
    """
    relative_path = str(file_path).replace('\\', '/')  # Normalize path separators
    is_directory = file_path.is_dir()
    
    ignored = False
    
    for rule in gitignore_rules:
        if rule.matches(relative_path, is_directory):
            ignored = not rule.is_negation
    
    return ignored


def create_gitignore_filter(base_path: Path) -> Result[callable, str]:
    """
    Create a gitignore filter function for the given base path.
    
    Returns a function that takes a file path and returns True if the file should be ignored.
    """
    gitignore_files = find_gitignore_files(base_path)
    all_rules = []
    
    for gitignore_file in gitignore_files:
        rules_result = parse_gitignore_file(gitignore_file)
        match rules_result:
            case Ok(rules):
                all_rules.extend(rules)
            case Error(error_msg):
                return Error(error_msg)
    
    all_rules_block = Block.of_seq(all_rules)
    
    def gitignore_filter(file_path: Path) -> bool:
        try:
            # Make path relative to base_path
            relative_path = file_path.relative_to(base_path)
            return is_ignored_by_gitignore(relative_path, all_rules_block)
        except ValueError:
            # Path is not relative to base_path, don't ignore
            return False
    
    return Ok(gitignore_filter)


def apply_filters(files: Block[FileInfo], config: TreeConfig, base_path: Optional[Path] = None) -> Block[FileInfo]:
    """
    Apply all configured filters to a list of files.
    
    This is a pure function that applies include patterns, exclude patterns,
    and gitignore rules based on the configuration.
    """
    def should_include(file_info: FileInfo) -> bool:
        # Check include patterns
        if config.include_patterns:
            if not matches_any_pattern(file_info.name, config.include_patterns, config.ignore_case):
                # For directories, also check if we should include for matching subdirectories
                if not (config.match_dirs and file_info.is_dir):
                    return False
        
        # Check exclude patterns
        if config.exclude_patterns:
            if matches_any_pattern(file_info.name, config.exclude_patterns, config.ignore_case):
                return False
        
        # Check gitignore
        if config.use_gitignore and base_path:
            gitignore_filter_result = create_gitignore_filter(base_path)
            match gitignore_filter_result:
                case Ok(gitignore_filter):
                    if gitignore_filter(file_info.path):
                        return False
                case Error(_):
                    pass  # Continue without gitignore filtering
        
        return True
    
    return pipe(
        files,
        seq.filter(should_include),
        Block.of_seq
    )


def is_hidden_file(name: str) -> bool:
    """Check if a file is hidden (starts with dot)."""
    return name.startswith('.')


def should_show_file(file_info: FileInfo, config: TreeConfig) -> bool:
    """
    Determine if a file should be shown based on configuration.
    
    This is a pure function that encapsulates all the logic for determining
    file visibility.
    """
    # Check if hidden file
    if is_hidden_file(file_info.name) and not config.show_all:
        return False
    
    # Check if directories only
    if config.dirs_only and not file_info.is_dir:
        return False
    
    # Check for errors
    if file_info.error:
        return True  # Show errors so user can see them
    
    return True
